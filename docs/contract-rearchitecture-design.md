# コントラクトリアーキテクチャ - データ・ロジック分離についての全体設計書

## はじめに
本ドキュメントはコントラクトのデータ管理コントラクトとロジック管理コントラクトを分離する上での全体設計書である。

本資料の元となった調査資料については「[検討]既存のコントラクトのストレージ方式と他の方式を比較し、最適なストレージ方式を検討する」を参照すること。

## 要約
2025/6現在、一つのコントラクトにデータ管理部分とロジック管理部分の両方が実装されており拡張性・メンテナンスビリティ向上のため、コントラクトの全体設計を見直す。

設計思想としては、**Eternal Storage Pattern**に従う。

### 設計変更による改善点
この設計変更により、コントラクトとして以下の改善が見込まれる：

1. **データリセットを伴わない柔軟なロジック変更が可能となる**
2. **外部コントラクトが参照するABIに影響を与えずにデータリセットが可能となる**
3. **ロジックコントラクトの分割が容易になることによるコントラクトの肥大化を予防できる**

### コントラクトの分類
設計変更に伴い、コントラクトは以下の5種類に分類される：

1. **データ管理コントラクトインターフェース**（以下Storageインターフェース）
2. **データ管理コントラクト**（以下Storageコントラクト）
3. **ロジック管理コントラクトインターフェース**（以下Logicインターフェース）
4. **ロジック管理コントラクト**（以下Logicコントラクト）
5. **ライブラリコントラクト**（以下ライブラリ）

## 設計内容

### 設計変更の背景
24/07にリリースしたコントラクトには以下の構造的な課題が存在していた：

1. **ロジックを変更する場合、コントラクトの再マイグレーションに伴いストレージもリセットする必要があるためバックアップリストアといった特殊な手順を踏む必要がある**

2. **コントラクトのサイズ上限に達した場合、コントラクトの分割等が必要となり、分割後のバックアップリストア機構の再検討など改修規模が格段に大きくなってしまう**

3. **外部から参照するコントラクトアドレス、ABIはコントラクトのアップデートが発生するたびに更新される**

### Eternal Storage Patternの採用理由
これらの課題解決へのアプローチとして、パブリックチェーンで利用される**Eternal Storage Pattern**を採用することで解決させる。

#### Eternal Storage Patternの利点
1. **ストレージ側コントラクトに影響を与えずに柔軟にロジック側コントラクトを修正&追加可能**
2. **ロジック側コントラクトに影響を与えずにストレージ側コントラクトの変更が可能**
3. **Transparent Proxy Pattern、Diamond Proxy Patternと比べて実装・運用が容易**

#### 採用理由
現在パブリックチェーンではTransparent Proxy PatternやDiamond Proxy Patternが主流であり、Eternal Storage Patternを新規で採用するプロジェクトはほとんど存在しない。

これはEternal Storage Patternのガス効率性の低さによるものであるが、本プロジェクトのDLT環境は**ガスレス**であるためTransparent Proxy PatternやDiamond Proxy Patternの最大の利点であるガス効率性の高さが利点とならない。

そのため、**運用・実装のシンプルさ**からEternal Storage Patternを採用している。

## 全体構成についての詳細説明

### コントラクトの分類と呼び出し制限について

| 分類 | 実装上の役割 | 呼び出し制限有無 |
|------|-------------|------------------|
| **1. Storageインターフェース** | Storageコントラクトを外部コントラクトから参照するためのInterfaceコントラクト<br>Logicコントラクト側は管理対象のStorageインターフェースを継承するStorageコントラクトとデプロイアドレスを参照することで外部呼び出しを行い、実際のStorageの操作を行う | - |
| **2. Storageコントラクト** | コントラクトデータモデルに沿ったデータ管理を行う<br>Storageコントラクトの内部で実装する関数はCRUDのみを想定し、直接のビジネスロジックは実装しない<br>上述のStorageインターフェースを継承する | **有**<br>対応するLogicコントラクトからのみ呼び出されることを想定し、登録外コントラクト・EOAからの呼び出しは制限される<br>バックアップリストア機能に関してはAdmin制限を設ける |
| **3. Logicインターフェース** | Contract Managerを経由してLogicコントラクト間で外部呼び出し・参照を行うためのInterfaceコントラクト<br>Contract Managerは各Logicインターフェースを継承するLogicコントラクトとデプロイアドレスを参照することで外部参照および外部呼び出しを行う | - |
| **4. Logicコントラクト** | ビジネスロジックの実装を行う<br>上述のStorageコントラクトの呼び出し・参照とContract Managerを経由した外部コントラクト連携を組み合わせることで、様々なビジネスロジックの実装を行う | **無**<br>ただしコントラクト間の呼び出しはContractManagerを経由して行われることと、関数ごとの呼び出し制限は存在する |
| **5. ライブラリ** | 楕円曲線暗号の計算、文字列操作、複雑な繰り返し処理といったStorageコントラクト側で実装する場合にコントラクトサイズへの影響が大きい処理を外部ユーティリティ化したコントラクト | **有**<br>Storageコントラクト以外から参照されることを想定しない |

### Logicコントラクトの各関数の外部用と内部用の区分について
Logicコントラクト内の各関数は大きく分けて以下の3分類ができる：

1. **外部コンポーネントが利用できる関数**
2. **他のコントラクトから利用できる関数**
3. **コントラクト内部のみで利用される関数**

このうち、3についてはprivate修飾子を付与することで参照制限をかけているが、1, 2はどちらもexternal修飾子を付与することになるため外部から見ると参照制限の差分が存在しない。

2の関数については外部コンポーネントから参照されることを避けたいため、以下のmodifierを実装することで外部コンポーネントのEOAから参照されることを防ぐ：

```solidity
modifier contractOnly () {
  require(_contractManager.senderIsContract(msg.sender));
  _;
}
```

2のコントラクト実装を行う場合は、上記`contractOnly`修飾子を付与することで外部のEOAから呼び出されることを防ぎつつ、対象の関数が外部コンポーネントから利用できる関数なのかそうでないのかの判別をつけやすくする。

## 機能一覧
Logicコントラクトが実装する機能一覧は「[基本設計]コントラクト 概要設計」に記載されているものを踏襲する。

## テーブル定義
Storageコントラクトが実装するテーブル定義は「[基本設計]コントラクト データ設計」に記載されているものを踏襲する。

## データモデル
Storageコントラクトが実装するデータモデルは「[基本設計]コントラクト データ設計」に記載されているものを踏襲する。

## 個々のコントラクトの実装で意識すべき設計観点
個々のコントラクトの実装において、横断的に注意すべき観点を記載する：

1. **署名検証処理について**
2. **call関数とsend関数の使い分け**
3. **Storageコントラクトの関数へのアクセス制限**
4. **Storageコントラクトのバックアップリストア関数へのアクセス制限**
5. **Logicコントラクトの個々の関数ごとの呼び出し制限**
6. **データモデル固有の注意点**
7. **ContractManagerとIFの位置付けと再デプロイ性**

### 1. 署名検証処理について
コントラクトには外部から渡された署名を利用した署名検証処理が実装されている。
この章では、主に署名検証の種類・チェック内容・チェック対象の業務について整理を行う。

#### 署名検証の種別
コントラクト上で行なっている署名検証処理には大きく以下の3つが存在する：

1. **Admin権限チェック**
2. **Role権限チェック**
3. **Account署名チェック**

なお、1. Admin権限チェックと2. Role権限チェックは実質的に同一のロジックを利用しているが、チェック対象の業務が大きく異なるため別のカテゴリとしている。

各関数がどのような情報を署名に含めることを求めているかどうかは「[基本設計]コントラクト 概要設計 | 署名フォーマットの精査結果」を参照。

どの関数がどの署名を必要とするかは外部の業務に依存するため、新たに署名を必要とする関数を作成する場合はAOレビュー等で外部コンポーネント側の実装と認識を合わせること。

#### 署名検証処理の詳細

| 署名検証処理 | チェック内容 | チェック対象の業務 |
|-------------|-------------|-------------------|
| **1. Admin権限チェック** | Role権限チェックは、引数で渡された署名からEOAを復元し、復元されたEOAがRole情報(Bytes32型の文字列)が規定に紐づいているかどうかを確認する。<br><br>Role情報はOpenzeppelinのAccessCtrlUpgradable.solの以下のマッピングで保存している：<br>```solidity<br>struct RoleData {<br>    mapping(address => bool) members;<br>    bytes32 adminRole;<br>}<br>mapping(bytes32 => RoleData) private _roles;<br>```<br><br>Role権限付与は各種業務の中で実行され、外部から渡される任意のEOAアドレスに権限を紐づける。<br><br>署名内に含まれるEOAアドレスの導出はOpenzeppelinのECDSAUpgradeable.solの以下の処理で実行される：<br>```solidity<br>address signer = ecrecover(hash, v, r, s);<br>```<br>※ecrecover()はEVMに組み込まれているPrecompiled Contractの一つで、引数のHash値とECDSA暗号のコンポーネント(v値, r値, s値)からEOAアドレスが取得できる。 | • Provider, Zoneデータ登録、更新<br>• Validatorデータ登録、更新<br>• Issuerデータ登録、更新<br>• ContractManagerが参照する外部コントラクトアドレスの設定・更新<br>• Bridgeコントラクトが参照する外部コントラクトアドレスの設定・更新<br>• バックアップ関数の実行<br>• リストア関数の実行 |
| **2. Role権限チェック** | 同上 | • Tokenデータ登録、更新<br>• Tokenの発行/償却/強制償却<br>• Accountの限度額更新<br>• Accountの累積限度額リセット<br>• AccountへのRole設定<br>• Account情報の更新<br>• IssuerコントラクトへのIssuerデータ登録、更新 |
| **3. Account署名チェック** | Account署名チェックは、引数で渡されたワンタイム公開鍵とアカウント署名を用いて二段階の検証を行い、正当なアカウントからの要求かどうかを確認する。<br><br>まず、アカウントIDとワンタイム公開鍵の対応関係を検証し、次にその公開鍵を使ってアカウント署名の正当性を確認する。<br><br>アカウント登録は各種業務の中で実行され、外部から渡される任意のアカウントID(Bytes32型の文字列)に公開鍵から導出されるEOAアドレスを紐づける。<br><br>アカウントIDとEOAの紐付け情報はAccessCtrl.solの以下のマッピングで保存している：<br>```solidity<br>mapping(bytes32 => address) private _idToAddress;<br>```<br><br>署名検証処理の詳しい実装については「[基本設計]コントラクト 概要設計 \| アカウントの外部署名の検証」を参照。 | • AccountへのBizアカウント紐付け<br>• 他AccountへのToken移転<br>• 他AccountへのToken移転許可 |

### 2. call関数とsend関数の使い分け
詳細な情報は「[基本設計]チェック関数と更新関数内での確認観点について」に記載している。

コントラクトの関数には大別して以下の二つが存在する：

- **call関数**
- **send関数**（以後更新関数と記載）

上記の更新関数の実行結果は非同期でクライアントに返ってくるため、事前に更新関数で扱う予定のデータを確認するためのcall関数（以後チェック関数と呼称）を用意し、更新関数の実行が問題ないかを同期的に確認する設計としている。

この章では更新関数とチェック関数でそれぞれどのような視点で値のチェックを行うかどうかを整理する。

関連ドキュメント：「[調査]Check関数の各チェック処理に不足がないか整理する」

#### 基本的な方針
基本的な方針は以下の2点である：

1. **チェック関数で確認できる部分は全て確認する**
2. **更新関数では実物資産と紐づくトークン操作に関わる関数のみチェックするようにする**
   - 例）送金に対して残高をチェックするケースでは、チェック関数実行時には十分な残高があり、更新時に万一不足していた場合に2重送金リスクがあるため、更新時にもチェックが必須

#### チェック関数でチェックする内容
**関連データの状態**
- パラメータに紐づくアカウントの状態がActiveであるか
- パラメータに紐づくアカウントのbalanceがパラメータのamount以下でないか
- パラメータに紐づくアカウントのallowanceがパラメータのamount以下でないか

**パラメータのバリデーション**
- パラメータのamountが0でないか
- パラメータのfromAccountIdが空文字でないか

#### 更新関数でチェックする内容
**実物資産と紐付き、二重更新が発生すると困る値**
- パラメータに紐づくアカウントのbalanceがパラメータのamount以下でないか
- パラメータに紐づくアカウントが移転対象のNFTを保有しているかどうか

### 3. Storageコントラクトの関数へのアクセス制限
Storageコントラクトのデータ読み取り・更新関数へのアクセスは、紐づくLogicコントラクトのみに限定する。
例えばValidatorStorageへのアクセスはValidatorLogicのみしか許可されない。

アクセス制限の実装は、コントラクトアドレスとmsg.senderの値を利用し、以下の手順で実装する：

1. **Storageコントラクトのデプロイ時にLogicコントラクトアドレスを保存**
2. **保存しているLogicコントラクトのアドレスとmsg.senderが一致していることを確認するmodifierを実装**
3. **Storageコントラクトの各関数では上記のmodifierを必ず呼び出すことで不正なアクセスを制限**

#### 具体的な実装例

**1. StorageLogicのストレージとしてLogicコントラクトのアドレスの値を保持**
```solidity
private address _validatorLogicAddr;

constructor(address logicAddr){
  require(validatorLogicAddr != address(0), Error.INVALID_ADDRESS);
  _validatorLogicAddr = validatorLogic
}

function setLogicAddress(address logicAddr) public onlyAdmin {
  require(validatorLogicAddr != address(0), Error.INVALID_ADDRESS);
}
```

**2. modifierとしてmsg.senderとLogicコントラクトのアドレスの値が一致することを確認**
```solidity
modifier validatorLobicOnly(){
  require(msg.sender == _validatorLogicAddr, Error.INVALID_CALLER_ADDRESS);
  _;
}
```

**3. Storageコントラクト内の関数でmodifierを利用**
```solidity
function getValidatorData(bytes32 validatorId) public view validatorLobicOnly returns (ValidatorData memory validatorData){
  ......(中略)
}
```

### 4. Storageコントラクトのバックアップリストア関数へのアクセス制限
Storageコントラクトのバックアップリストア関連の関数については、Logicコントラクトからの呼び出し制限に加えて、呼び出しアカウントがAdmin権限を保持ししているかどうかを確認する。

アクセス制限の実装は、Adminのみが管理する秘密鍵による署名データとAccessCtrlで管理するRole情報を利用し、以下の手順で実装する：

1. **AccessCtrlのデプロイ時に特定のEOAをAdminRoleとして登録**
2. **modifierとして、署名データを受け取り署名者が正しいRoleを保持しているか確認する処理を実装**
3. **Storageコントラクト内のバックアップ・リストア関数は、2で実装したmodifierを利用することで不正なアクセスを防止**

#### 具体的な実装例

**1. AccessCtrlのデプロイ時に特定のEOAをAdminRoleとして登録**
```solidity
function initialize(IContractManager contractManager, address eoaAdmin) public initializer {
    _contractManager = contractManager;
    _setupRole(_ROLE_ADMIN, eoaAdmin);
}
```

**2. modifierとして、署名データを受け取り署名者が正しいRoleを保持しているか確認**
```solidity
modifier adminOnly(uint256 deadline, bytes memory signature) {
    bytes32 hash = keccak256(abi.encode(_STRING_ACCOUNT_SYNC, deadline));
    (bool has, string memory err) = _accessCtrl.checkAdminRole(hash, deadline, signature);
    require(bytes(err).length == 0, err);
    require(has, "not admin role");
    _;
}
```

**3. Storageコントラクト内のバックアップ・リストア関数でmodifierを利用**
```solidity
function getValidatorAll(
    uint256 index,
    uint256 deadline,
    bytes memory signature
  ) public view adminOnly(deadline, signature) returns (
    ValidatorData[] memory validatorData
  ){
  ......(中略)
}
```

### 5. Logicコントラクトの個々の関数ごとの呼び出し制限
Logicコントラクトの各関数は他のLogicコントラクトからのみ呼ぶことができるものが存在する。
たとえばAccountLogicのaddAccountはValidatorLogicのaddAccountのみ呼び出すことができる。

アクセス制限の実施は、ContractManager経由で取得した各Logicコントラクトのアドレスとmsg.senderの値を利用し、判別を行う。

1. **Logicコントラクトのデプロイ後、ContractManagerのsetContractで各コントラクトのアドレスを登録**
2. **各Logicコントラクト内で、msg.senderの値と呼び出し対象のLogicコントラクトのアドレスが一致しているかどうかを確認**

#### 具体的な実装例

**1. ContractManagerのsetContractで各コントラクトのアドレスを登録**
```solidity
contract ContractManager is Initializable {
  address private _validatorLogic;
  ...中略

  function setContract(
    address validatorLogic,
    ...中略
  ) public {
    {
      _validatorLogic = validatorLogic;
      ...中略
    }
  }

  function validatorLogic() public external view returns (address) {
    return _validatorLogic;
  }
}
```

**2. 各Logicコントラクト内でmsg.senderの値と呼び出し対象のLogicコントラクトのアドレスが一致しているかどうかを確認**
```solidity
contract AccountLogic is Initializable {
  function addAccount (bytes32 accountId, AccountData memory accountData) external override {
      require(msg.sender == _contractManager.validatorLogic(), Errror.SENDER_IS_NOT_VALIDATOR);
  }
}
```

### 6. データモデル固有の注意点
コントラクトの内部で保持するストレージとしてmappingを利用する場合、**必ずkeyのindexとなる配列も保持し、同時に双方を更新すること**。

#### 具体的な実装例
```solidity
contract AccountStorage is Initializable {
  mapping(bytes32 => AccountData) private _accountData; // mappingのストレージ
  bytes32[] private _accountIds;　　　　　　　　　　　　　　//mappingのkeyを保存するIndexとなる配列
}
```

#### 実装理由
このような実装とする理由は、Solidityの言語上の制約による。

Solidityのmappingはあらゆるパターンのkeyとそこに紐づくValueの値を紐づけるだけの機能であり、他の言語で見られる仕様のようなmappingとなっていない（詳細は[Types — Solidity 0.8.31 documentation](https://docs.soliditylang.org/en/v0.8.31/types.html#mapping-types)を参照）。

そのため、Solidityのmappingにはlengthやkeyの一覧といった概念が存在しない。

しかし実業務上ではmappingに保存しているkeyの一覧を取得したいというニーズは存在するため、対応策として**keyの一覧を保管する配列を別で定義することで管理を行なっている**。

### 7. ContractManagerとIFの位置付けと再デプロイ性
ContractManagerは各Logicコントラクト間の連携を管理する中核的な役割を担う。

- **各Logicインターフェースを継承するLogicコントラクトとデプロイアドレスを参照**
- **外部参照および外部呼び出しを仲介**
- **コントラクトの再デプロイ時にはContractManagerの設定を更新することで新しいコントラクトアドレスに切り替え可能**

これにより、個別のLogicコントラクトを再デプロイしても、他のコントラクトへの影響を最小限に抑えることができる。

## まとめ
本設計により、以下の利点を実現する：

1. **保守性の向上**: データとロジックの分離により、それぞれを独立して変更可能
2. **拡張性の向上**: 新しいLogicコントラクトの追加が容易
3. **運用性の向上**: バックアップリストア機能の簡素化
4. **セキュリティの向上**: 適切なアクセス制限による不正アクセスの防止

Eternal Storage Patternの採用により、DLT環境の特性を活かした効率的なコントラクト設計を実現している。
```
