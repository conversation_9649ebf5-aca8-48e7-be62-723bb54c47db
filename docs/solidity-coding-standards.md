# Solidityコーディング規約

## はじめに
本プロジェクトでのSolidityコーディング規約について規定する。

## コーディング規約

### ディレクトリ・ファイル
- Hardhatのディレクトリルールに従い、`contracts/`以下にSolidityファイルを置く
- 1コントラクトは1Solidityファイルに書く（1つのファイルに複数のコントラクトを書かない）
- ファイル名はコントラクト名と同じにする
- コントラクトファイルの最上位には必ず以下の記載を追加すること：

```solidity
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
```

### コメント
contractとfunctionにはコメントをNatSpec形式で書く

#### contractコメント例
```solidity
/**
 * @dev コントラクトの説明
 */
contract Xxx {
```

#### functionコメント例
外部公開する関数には必ず付ける。internalはなるべく付ける。

```solidity
/**
 * @dev 関数の説明
 * @param id ユーザID
 * @return true:処理成功
 */
function Yyy(uint256 id) external returns (bool) {
```

### 名前
- storage変数は、アンダースコア（`_`）で始める
- internal関数は、アンダースコア（`_`）で始める

### 外部署名
引数に関数呼び出し元の署名を付ける場合がある。ここでは「外部署名」と呼ぶ。

外部署名の関数は、引数の最後に2つの引数を付ける。型と名前を合わせること：
- `uint256 deadline`：署名を行ったepochのタイムスタンプ。単位は秒
- `bytes signature`：引数をハッシュ計算した値に呼び出し元の秘密鍵でEthereumのメッセージ署名を行った値

ハッシュ計算は、引数の先頭からdeadlineまでを対象とする。

### イベント
- externalの関数でトランザクションが発生する呼び出しについてはイベントをemitする
- それ以外は場合による

## 実装時の方針

### 外部コントラクトの呼び出し

#### 設計思想
外部コントラクト呼び出しには呼び出し先コントラクトのインスタンスが必要であり、各コントラクトがそれぞれで接続先のインスタンスを保持した場合に管理がとても煩雑になってしまう。また、呼び出し先のコントラクトのアドレスが変更した場合に都度呼び出し元コントラクトで管理しているコントラクトアドレスを更新する必要が出てしまう。

呼び出しの中継を行うコントラクトであるContractManagerを作成することで各コントラクトは呼び出し先コントラクト全てを管理する必要がなくなり、各コントラクトはContractManagerのみを意識すれば外部コントラクトの呼び出しを可能とした。

#### 実装方法
外部コントラクトの呼び出しはContractManagerを介して行うこと：

```solidity
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
import "./interfaces/IContractManager.sol"; // ContractManagerのインターフェースをimport

contract Issuer {
  IContractManager private _contractManager; // ContractManagerのインターフェース型の変数を宣言
  
  function initialize(IContractManager contractManager) public initializer {
    _contractManager = contractManager; // デプロイ時に実行するinitialize()で_contractManagerにContractManagerのデプロイしたアドレスを代入する
  }
  
  function setAccountEnabled() external override {
     _contractManager.account().setEnabled(); // _contractManagerからAccountコントラクトのsetEnabled()を呼び出す
  }
}
```

- 初回デプロイのタイミングでContractManagerのsetContractを実行し、各コントラクトのアドレスをContractManagerに登録しておく
- 各コントラクトはデプロイ時の初期処理としてIContractManager型の_contractManager変数にContractManagerのアドレスを登録
- 実行時はContractManagerのアドレスからContractManagerを呼び出し、ContractManagerはsetContractで登録された各コントラクトのアドレスからexternal関数やpublic関数を呼び出す

### 関数修飾子

#### 設計思想
- 外部からアクセスを遮断したいストレージ上の変数（マッピング含む）は基本的にprivateで宣言する
- それ以外の場合は、コントラクト外部・内部・継承先で呼び出しが行われるかの用途に沿って修飾子を設定する
- externalはpublicと比べてガスの消費量が低いため、同一コントラクトで呼び出しを行わない場合は基本的にはexternalを利用する

#### 各修飾子の利用可能範囲

| 修飾子 | コントラクト外部 | コントラクト内部 | 継承先 |
|--------|------------------|------------------|--------|
| private | × | ○ | × |
| internal | × | ○ | ○ |
| public | ○ | ○ | ○ |
| external | ○ | × | × |

### アクセスコントロール

#### 設計思想
DCYプラットフォーム上ではトランザクションの送信者（`msg.sender`）と実際の要求元が異なるため、msg.senderに依存しない本人確認を行う必要があった。

msg.senderに依存しない本人確認方法の実装として、OpenzeppelinのAccessControl.solによるRoleベースの本人確認を採用した。

#### 実装方法
Txの送り元の検証にはTxの特別な場合を除いてmsg.senderを利用したアドレス検証は行わず、OpenzeppelinのAccessControlUpgradable.solを継承したAccessCtrl.solで行う。

```solidity
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";

contract AccessCtrl is Initializable, AccessControlUpgradeable {
    function addAdminRole(
        address eoaNew,
        uint256 deadline,
        bytes memory signature
    ) external {
        bytes32 hash = keccak256(abi.encode(eoaNew, deadline));
        (bool has, string memory errTmp) = _checkRole(_ROLE_ADMIN, hash, deadline, signature); // 事前にTx送り元の権限チェックを行う
        require(bytes(errTmp).length == 0, errTmp);
        require(has, Error.ACTRL_NOT_ADMIN_ROLE);
        _setupRole(_ROLE_ADMIN, eoaNew); // _setUpRole()を実行し、EOAアドレスと権限を結びつける
    }
}
```

権限削除の場合：
```solidity
function delAdminRole(address eoaDel) external {
    require(hasRole(DEFAULT_ADMIN_ROLE, msg.sender), Error.ACTRL_BAD_ROLE); // msg.senderがDEFAULT_ADMIN_ROLEの権限を持っているか確認
    require(!hasRole(DEFAULT_ADMIN_ROLE, eoaDel), Error.ACTRL_BAD_ROLE); // admin role権限を持つ場合は削除できない
    revokeRole(_ROLE_ADMIN, eoaDel); // revokeRole()を実行し、EOAアドレスと権限を結びつける
}
```

権限チェック：
```solidity
function _checkRole(
    bytes32 role,
    bytes32 hash,
    uint256 deadline,
    bytes memory signature
) internal view returns (bool has, string memory err) {
    if (role == 0) {
        return (false, Error.ACTRL_NOT_ROLE); // 引数として渡されたRoleの値が不正でないかチェック
    }
    if (deadline < block.timestamp) {
        return (false, Error.ACTRL_SIG_TIMEOUT); // 署名の有効期限と現在時刻を比較し、有効期限切れとなっていないか確認
    }
    err = _sigVerify(signature); // signatureが楕円曲線を使用して作成されている事を確認する
    if (bytes(err).length != 0) {
        return (false, err);
    }
    address addr = _recoverAddr(hash, signature); // ハッシュ値と署名からアドレスを復元し、アドレスがRoleとして渡された権限を保有しているかチェックする
    if (addr == address(0)) {
        return (false, Error.ACTRL_BAD_SIG); 
    }
    has = hasRole(role, addr);
}
```

### Stack Too Deepの回避

#### 概要
Stack Too Deepエラーは、内部で複数の変数を扱うような複雑なコントラクトを作成する上でほぼ確実に遭遇するエラーである。

EVMのStackではメモリ上の上位16個のelementまでしか操作できず、elementの操作数が16個を超えるとstackが扱える範囲の深さを超える（too deep）状態となってしまいエラーとなる。

#### 回避方法
基本的には関数の切り出しを行うことで対応する。

1. **関数の切り出し**
   - 関数内で一つのStackで操作できる要素が制限されていることがエラーの原因であるため、一部の処理を別関数に切り出す
   - パフォーマンスは多少落ちるが、実装としては最も容易

2. **引数の値をabi.encodeで結合**
   - 複数の引数を事前にweb3.jsやweb3j側でabi.encode()などで結合し、利用するSolidity関数側でパースする
   - パース時に元のデータが崩れないように注意する必要がある

3. **メモリへの一時的な移動**
   - InlineAssemblyを利用して引数をStackからMemoryに一時的に移動させ、処理中はMemoryから値を読み込む
   - パフォーマンスは最も高いが、実装難易度は高い

## チェック項目

### 署名
署名を引数にとる関数の場合：
- タイムアウトしていないこと
- 署名が期待した権限を持ったEOAにより行われていること
- Admin以外は署名したEOAを探すためのIDを持つはず（たぶん第1引数、例外あり）
- 権限を持つEOAがenabledであること
- checkRole()では署名の確認は行うが、enabledのチェックまで行っていない

**注意事項：**
- CurrencyやTokenは例外的に「属するID」があるので注意
- たとえばCurrencyはProviderに属していて、署名はProviderが行うことが多い
- その場合、CurrencyIDを使って紐付くProviderIDを取得するが、そのProviderIDは未登録かもしれない
- 未登録の場合はSolidityでデフォルト値が割り当てられるため、uint256型は0が入っている
- なので、署名の確認前に`_data[CurrencyID].provID`が0でないことを確認している

### ID値

#### 値全般
- 0を許容しないことが多いので、0チェック

#### provID
- provIDが登録済みか
- enabledかどうか（場合による）

#### currID
- currIDが登録済みか
- 関連するprovIDが登録済みか
- Provider側でもcurrIDを登録しているか
- enabledかどうか（場合による）

#### issuerID
- issuerIDが登録済みか
- enabledかどうか（場合による）

#### tokenID
- tokenIDが登録済みか
- 関連するIssuerIDが登録済みか
- Issuer側でもtokenIDを登録しているか
- enabledかどうか（場合による）

#### userID
- userIDが登録済みか
- enabledかどうか（場合による）

### enabled
- 引数に署名をとる場合、その署名をしたEOAはenabledであること
- 値の変更する場合、その対象はenabledであること。ただしenabledを変更する関数の場合はfalseでも変更できる

### 登録
- arrayにpush()する場合、まだ登録されていない値であること
- mapに追加する場合：
  - **パターン1：** 追加時に必ず変更する要素を1つ以上持つようにしておく。そして、その要素がデフォルト値であること（mapは未追加の値でも参照できてしまうため）
  - **パターン2：** ID（array）とデータ（map）をセットで保存するような場合、IDのarrayで登録済みかどうかチェックする

### 参照
- arrayの場合、`.length`で要素数の範囲を超えないこと
- mapの場合、初期値以外の要素が存在する（登録パターン1）か、IDのarrayが登録済み（登録パターン2）であること

### 計算
- 加算の場合、オーバーフローしないこと
- 減算の場合、アンダーフローしないこと
- SafeMathを使っておくと避けられるが、エラーも返したいのでアンダーフローは手前でチェックしたい

**注意事項：**
- 2020.2.1現在、SafeMathUpgradeableがusing句を使用しても動作しない問題が発生している。そのためadd()やsub()は使用せず計算を行い、オーバーフローの検知は演算の前後で行うようにする
- 2023.6.1現在、プロジェクトではSolidity0.8以上を利用しており、0.8以上のSolidityではオーバーフロー・アンダーフローのチェックはコンパイラー側で行うため特に気にする必要はない
- ただし、デフォルトでオーバーフローやアンダーフローのチェックが行われることによりGas代が高くなるため、わざわざチェックするほどの値でない場合は`unchecked()`で囲むことでGas代を節約できる

## チェック関数

### プロバイダIDの存在および有効性チェック
`Provider.hasProvID(provID, chkEnabled)`
- provIDがプロバイダとして登録されているかどうか
- chkEnabled=trueの場合、provIDがenabledかどうかもチェックする

### プロバイダが登録したユーザIDかどうか
`Provider.hasProvUserID(provID, userID)`
- userIDはprovIDが登録したのかどうか（現在provIDが有効であること）
- Provider.hasProvID(provID, true)も行うので、provIDが無効の場合には登録チェックされない

### プロバイダ権限チェック
`Provider.checkRole(provID, hash, deadline, signature)`
- 署名の期限切れチェック
- 署名がprovIDのものかチェック
- Provider.hasProvID()は行わない
- provIDが未登録の場合は署名チェックに必要な情報が揃わないためエラーになる

### 発行者IDの存在および有効性チェック
`Issuer.hasIssuerID(issuerID, chkEnabled)`
- issuerIDが発行者として登録されているかどうか
- chkEnabled=trueの場合、issuerIDがenabledかどうかもチェックする

### 発行者権限チェック
`Issuer.checkRole(issuerID, hash, deadline, signature)`
- 署名の期限切れチェック
- 署名がissuerIDのものかチェック
- Issuer.hasIssuerID()は行わない
- issuerIDが未登録の場合は署名チェックに必要な情報が揃わないためエラーになる

### ユーザIDの存在および有効性チェック
`User.hasUserID(userID, chkEnabled)`
- userIDがユーザとして登録されているかどうか
- chkEnabled=trueの場合、userIDがenabledかどうかもチェックする
- 登録したプロバイダが有効かどうかはチェックしない

### ユーザ署名チェック
`User.checkSigner(userID, hash, deadline, signature)`
- 署名の期限切れチェック
- 署名がuserIDのものかチェック
- 他の似たような関数はRoleなのだが、ユーザは権限というわけでもないので名前が違う
- また実装的な話になるが、Role系はOpenZeppelinのAccessControlライブラリを使っているのだが、Userは使っていないというのもある

### デジタル通貨IDの存在および有効性チェック
`Currency.hasCurrencyID(currID, chkEnabled, chkProvID)`
- currIDがデジタル通貨IDとして登録されているかどうか
- chkEnabled=trueの場合、currIDがenabledかどうかもチェックする
- chkProvID=trueの場合、currIDを登録したプロバイダがenabledかどうかもチェックする
- なお、デジタル通貨専用の権限はない

### 供託金関係のチェック
供託金は発行者IDと直接関連しているため、専用のチェック関数はない。

### トークンIDの存在および有効性チェック
`Token.hasTokenID(tokenID, chkEnabled)`
- tokenIDがデジタル通貨IDとして登録されているかどうか
- また、tokenIDを登録した発行者の有効性チェックも行われる
- chkEnabled=trueの場合、tokenIDがenabledかどうかもチェックする

### ユーザIDのbalanceにアクセスできるか
`Token.hasTokenUserID(tokenID, userID)`
- tokenIDの有効性チェック
- ユーザIDの有効性チェック
- tokenIDの持つuserIDの有効性チェック
- tokenIDの持つuserIDの本人確認済みチェック
- balanceを操作する場合、この関数を使ってチェックする

**注意事項：**
- Tokenコントラクト内ではチェック呼び出しを簡略にして、送金でuserIDにfromとtoがあるので、どちらか片方はこちらを使い、もう片方は簡易チェックで済ませるのがよいだろう
- それ以外ではCurrencyからになるが、Currencyからは個別のbalanceについて確認することはないので、気にしなくてよいだろう
```
